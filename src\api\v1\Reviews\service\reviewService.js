const Review = require('../model/reviewModel');
const { getNextSequenceValue } = require('../counter/reviewCounter');
const logger = require('../../../common/utils/logger');
const { v4: uuidv4 } = require('uuid');

/**
 * Create a new review
 * @param {Object} reviewData - Review data
 * @param {string} customerId - Customer ID from auth
 * @returns {Promise<Object>} Created review
 */
const createReview = async (reviewData, customerId) => {
    try {
        // Check if customer has already reviewed this booking
        const existingReview = await Review.findOne({
            customerId,
            bookingId: reviewData.bookingId,
            isDeleted: false,
        });

        if (existingReview) {
            throw new Error('You have already reviewed this booking.');
        }

        // Generate review ID
        const sequenceValue = await getNextSequenceValue('reviewId');
        const reviewId = `REV_${sequenceValue.toString().padStart(6, '0')}`;

        // Process images if provided
        const processedImages = reviewData.images?.map(image => ({
            imageId: uuidv4(),
            imageUrl: image.imageUrl,
            imageCaption: image.imageCaption || '',
            uploadedAt: new Date(),
        })) || [];

        // Create review object
        const review = new Review({
            reviewId,
            customerId,
            providerId: reviewData.providerId,
            serviceId: reviewData.serviceId,
            bookingId: reviewData.bookingId,
            rating: reviewData.rating,
            title: reviewData.title || '',
            comment: reviewData.comment,
            images: processedImages,
            status: 'approved', // Auto-approve for now
            isVerifiedPurchase: true,
            reviewDate: new Date(),
        });

        const savedReview = await review.save();
        logger.info(`Review created successfully. ID: ${reviewId}`);
        
        return savedReview;
    } catch (error) {
        logger.error(`Error creating review: ${error.message}`);
        throw error;
    }
};

/**
 * Get reviews with filtering and pagination
 * @param {Object} query - Query filters
 * @param {string} sortBy - Sort field
 * @param {number} sortDirection - Sort direction (1 or -1)
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} Reviews and metadata
 */
const getReviews = async (query, sortBy = 'reviewDate', sortDirection = -1, page = 1, limit = 10) => {
    try {
        const skip = (page - 1) * limit;
        const sortObj = { [sortBy]: sortDirection };

        // Build query filter - ensure only approved and non-deleted reviews for public access
        const filter = {
            isDeleted: false,
            status: query.status || 'approved',
            ...query
        };

        // Use aggregation pipeline for better data formatting and computed fields
        const pipeline = [
            { $match: filter },
            {
                $addFields: {
                    // Calculate helpful vote counts
                    helpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'helpful'] }
                            }
                        }
                    },
                    notHelpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'not_helpful'] }
                            }
                        }
                    },
                    // Calculate helpfulness percentage
                    helpfulnessPercentage: {
                        $cond: {
                            if: { $eq: [{ $size: '$helpfulVotes' }, 0] },
                            then: 0,
                            else: {
                                $round: [
                                    {
                                        $multiply: [
                                            {
                                                $divide: [
                                                    {
                                                        $size: {
                                                            $filter: {
                                                                input: '$helpfulVotes',
                                                                cond: { $eq: ['$$this.voteType', 'helpful'] }
                                                            }
                                                        }
                                                    },
                                                    { $size: '$helpfulVotes' }
                                                ]
                                            },
                                            100
                                        ]
                                    },
                                    1
                                ]
                            }
                        }
                    },
                    // Format review date for frontend
                    formattedReviewDate: {
                        $dateToString: {
                            format: '%Y-%m-%d',
                            date: '$reviewDate'
                        }
                    },
                    // Calculate time since review
                    daysSinceReview: {
                        $floor: {
                            $divide: [
                                { $subtract: [new Date(), '$reviewDate'] },
                                86400000 // milliseconds in a day
                            ]
                        }
                    },
                    // Add image count
                    imageCount: { $size: '$images' },
                    // Check if provider has responded
                    hasProviderResponse: {
                        $cond: {
                            if: { $ifNull: ['$providerResponse', false] },
                            then: true,
                            else: false
                        }
                    }
                }
            },
            {
                $project: {
                    // Core review data
                    reviewId: 1,
                    customerId: 1,
                    providerId: 1,
                    serviceId: 1,
                    bookingId: 1,
                    rating: 1,
                    title: 1,
                    comment: 1,
                    status: 1,
                    isVerifiedPurchase: 1,
                    isEdited: 1,
                    reviewDate: 1,
                    formattedReviewDate: 1,
                    daysSinceReview: 1,

                    // Images (limit to essential data for performance)
                    images: {
                        $map: {
                            input: '$images',
                            as: 'img',
                            in: {
                                imageId: '$$img.imageId',
                                imageUrl: '$$img.imageUrl',
                                imageCaption: '$$img.imageCaption'
                            }
                        }
                    },
                    imageCount: 1,

                    // Provider response (if exists)
                    providerResponse: {
                        $cond: {
                            if: '$hasProviderResponse',
                            then: {
                                responseText: '$providerResponse.responseText',
                                responseDate: '$providerResponse.responseDate',
                                isEdited: '$providerResponse.isEdited'
                            },
                            else: null
                        }
                    },
                    hasProviderResponse: 1,

                    // Helpful votes data
                    helpfulVotesCount: 1,
                    notHelpfulVotesCount: 1,
                    helpfulnessPercentage: 1,
                    totalVotes: { $add: ['$helpfulVotesCount', '$notHelpfulVotesCount'] },

                    // Timestamps
                    createdAt: 1,
                    updatedAt: 1
                }
            },
            { $sort: sortObj },
            { $skip: skip },
            { $limit: limit }
        ];

        // Execute aggregation
        const reviews = await Review.aggregate(pipeline);

        // Get total count for pagination
        const total = await Review.countDocuments(filter);

        // Calculate pagination metadata
        const totalPages = Math.ceil(total / limit);
        const hasNext = page < totalPages;
        const hasPrev = page > 1;

        logger.info(`Fetched ${reviews.length} reviews from page ${page} with enhanced data`);

        return {
            reviews,
            total,
            page,
            pages: totalPages,
            hasNext,
            hasPrev,
            // Additional metadata for frontend
            metadata: {
                currentPage: page,
                itemsPerPage: limit,
                totalItems: total,
                totalPages,
                hasNextPage: hasNext,
                hasPreviousPage: hasPrev,
                startIndex: skip + 1,
                endIndex: Math.min(skip + limit, total)
            }
        };
    } catch (error) {
        logger.error(`Error fetching reviews: ${error.message}`);
        throw error;
    }
};

/**
 * Get review by ID with enhanced data
 * @param {string} reviewId - Review ID
 * @returns {Promise<Object>} Review data
 */
const getReviewById = async (reviewId) => {
    try {
        const pipeline = [
            {
                $match: {
                    reviewId,
                    isDeleted: false
                }
            },
            {
                $addFields: {
                    // Calculate helpful vote counts
                    helpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'helpful'] }
                            }
                        }
                    },
                    notHelpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'not_helpful'] }
                            }
                        }
                    },
                    // Calculate helpfulness percentage
                    helpfulnessPercentage: {
                        $cond: {
                            if: { $eq: [{ $size: '$helpfulVotes' }, 0] },
                            then: 0,
                            else: {
                                $round: [
                                    {
                                        $multiply: [
                                            {
                                                $divide: [
                                                    {
                                                        $size: {
                                                            $filter: {
                                                                input: '$helpfulVotes',
                                                                cond: { $eq: ['$$this.voteType', 'helpful'] }
                                                            }
                                                        }
                                                    },
                                                    { $size: '$helpfulVotes' }
                                                ]
                                            },
                                            100
                                        ]
                                    },
                                    1
                                ]
                            }
                        }
                    },
                    // Format dates
                    formattedReviewDate: {
                        $dateToString: {
                            format: '%Y-%m-%d',
                            date: '$reviewDate'
                        }
                    },
                    daysSinceReview: {
                        $floor: {
                            $divide: [
                                { $subtract: [new Date(), '$reviewDate'] },
                                86400000
                            ]
                        }
                    },
                    imageCount: { $size: '$images' },
                    hasProviderResponse: {
                        $cond: {
                            if: { $ifNull: ['$providerResponse', false] },
                            then: true,
                            else: false
                        }
                    }
                }
            }
        ];

        const reviews = await Review.aggregate(pipeline);
        const review = reviews[0];

        if (!review) {
            throw new Error('Review not found.');
        }

        logger.info(`Fetched review with ID: ${reviewId}`);
        return review;
    } catch (error) {
        logger.error(`Error fetching review by ID: ${error.message}`);
        throw error;
    }
};

/**
 * Update review (only by original reviewer)
 * @param {string} reviewId - Review ID
 * @param {Object} updateData - Update data
 * @param {string} customerId - Customer ID from auth
 * @returns {Promise<Object>} Updated review
 */
const updateReview = async (reviewId, updateData, customerId) => {
    try {
        const review = await Review.findOne({ 
            reviewId, 
            customerId,
            isDeleted: false 
        });

        if (!review) {
            throw new Error('Review not found or you do not have permission to update it.');
        }

        // Update allowed fields
        if (updateData.rating !== undefined) review.rating = updateData.rating;
        if (updateData.title !== undefined) review.title = updateData.title;
        if (updateData.comment !== undefined) review.comment = updateData.comment;
        
        // Process images if provided
        if (updateData.images !== undefined) {
            review.images = updateData.images.map(image => ({
                imageId: uuidv4(),
                imageUrl: image.imageUrl,
                imageCaption: image.imageCaption || '',
                uploadedAt: new Date(),
            }));
        }

        // Mark as edited
        review.isEdited = true;
        review.editedAt = new Date();

        const updatedReview = await review.save();
        logger.info(`Review updated successfully. ID: ${reviewId}`);
        
        return updatedReview;
    } catch (error) {
        logger.error(`Error updating review: ${error.message}`);
        throw error;
    }
};

/**
 * Soft delete review
 * @param {string} reviewId - Review ID
 * @param {string} userId - User ID (customer or admin)
 * @param {string} userType - User type (customer, admin)
 * @returns {Promise<Object>} Deleted review
 */
const deleteReview = async (reviewId, userId, userType = 'customer') => {
    try {
        const query = { reviewId, isDeleted: false };
        
        // Only allow customers to delete their own reviews
        if (userType === 'customer') {
            query.customerId = userId;
        }

        const review = await Review.findOne(query);

        if (!review) {
            throw new Error('Review not found or you do not have permission to delete it.');
        }

        review.isDeleted = true;
        review.deletedAt = new Date();
        review.deletedBy = userId;

        const deletedReview = await review.save();
        logger.info(`Review deleted successfully. ID: ${reviewId}`);
        
        return deletedReview;
    } catch (error) {
        logger.error(`Error deleting review: ${error.message}`);
        throw error;
    }
};

/**
 * Add provider response to review
 * @param {string} reviewId - Review ID
 * @param {Object} responseData - Response data
 * @param {string} providerId - Provider ID from auth
 * @returns {Promise<Object>} Updated review
 */
const addProviderResponse = async (reviewId, responseData, providerId) => {
    try {
        const review = await Review.findOne({
            reviewId,
            providerId,
            isDeleted: false
        });

        if (!review) {
            throw new Error('Review not found or you do not have permission to respond.');
        }

        if (review.providerResponse) {
            throw new Error('You have already responded to this review.');
        }

        const responseId = uuidv4();
        review.providerResponse = {
            responseId,
            providerId,
            responseText: responseData.responseText,
            responseDate: new Date(),
        };

        const updatedReview = await review.save();
        logger.info(`Provider response added to review. ID: ${reviewId}`);

        return updatedReview;
    } catch (error) {
        logger.error(`Error adding provider response: ${error.message}`);
        throw error;
    }
};

/**
 * Add helpful vote to review
 * @param {string} reviewId - Review ID
 * @param {Object} voteData - Vote data
 * @param {string} userId - User ID from auth
 * @returns {Promise<Object>} Updated review
 */
const addHelpfulVote = async (reviewId, voteData, userId) => {
    try {
        const review = await Review.findOne({
            reviewId,
            isDeleted: false
        });

        if (!review) {
            throw new Error('Review not found.');
        }

        // Check if user has already voted
        const existingVoteIndex = review.helpfulVotes.findIndex(
            vote => vote.userId === userId
        );

        if (existingVoteIndex !== -1) {
            // Update existing vote
            review.helpfulVotes[existingVoteIndex].voteType = voteData.voteType;
            review.helpfulVotes[existingVoteIndex].votedAt = new Date();
        } else {
            // Add new vote
            review.helpfulVotes.push({
                userId,
                voteType: voteData.voteType,
                votedAt: new Date(),
            });
        }

        const updatedReview = await review.save();
        logger.info(`Helpful vote added to review. ID: ${reviewId}`);

        return updatedReview;
    } catch (error) {
        logger.error(`Error adding helpful vote: ${error.message}`);
        throw error;
    }
};

/**
 * Get review analytics for a service or provider
 * @param {Object} filters - Filters (serviceId, providerId)
 * @returns {Promise<Object>} Analytics data
 */
const getReviewAnalytics = async (filters) => {
    try {
        const matchStage = {
            isDeleted: false,
            status: 'approved',
            ...filters
        };

        const analytics = await Review.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' },
                    ratingDistribution: {
                        $push: '$rating'
                    },
                    totalHelpfulVotes: { $sum: '$helpfulCount' },
                }
            },
            {
                $project: {
                    _id: 0,
                    totalReviews: 1,
                    averageRating: { $round: ['$averageRating', 2] },
                    totalHelpfulVotes: 1,
                    ratingBreakdown: {
                        $let: {
                            vars: {
                                ratings: '$ratingDistribution'
                            },
                            in: {
                                '5': {
                                    $size: {
                                        $filter: {
                                            input: '$$ratings',
                                            cond: { $eq: ['$$this', 5] }
                                        }
                                    }
                                },
                                '4': {
                                    $size: {
                                        $filter: {
                                            input: '$$ratings',
                                            cond: { $eq: ['$$this', 4] }
                                        }
                                    }
                                },
                                '3': {
                                    $size: {
                                        $filter: {
                                            input: '$$ratings',
                                            cond: { $eq: ['$$this', 3] }
                                        }
                                    }
                                },
                                '2': {
                                    $size: {
                                        $filter: {
                                            input: '$$ratings',
                                            cond: { $eq: ['$$this', 2] }
                                        }
                                    }
                                },
                                '1': {
                                    $size: {
                                        $filter: {
                                            input: '$$ratings',
                                            cond: { $eq: ['$$this', 1] }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ]);

        const result = analytics[0] || {
            totalReviews: 0,
            averageRating: 0,
            totalHelpfulVotes: 0,
            ratingBreakdown: { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0 }
        };

        logger.info(`Fetched review analytics for filters: ${JSON.stringify(filters)}`);
        return result;
    } catch (error) {
        logger.error(`Error fetching review analytics: ${error.message}`);
        throw error;
    }
};

/**
 * Get overall rating summary for a service or provider
 * @param {Object} filters - Filters (serviceId, providerId)
 * @returns {Promise<Object>} Rating summary data
 */
const getOverallRating = async (filters) => {
    try {
        const matchStage = {
            isDeleted: false,
            status: 'approved',
            ...filters
        };

        const ratingData = await Review.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' },
                    ratingDistribution: {
                        $push: '$rating'
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalReviews: 1,
                    averageRating: { $round: ['$averageRating', 2] },
                    ratingBreakdown: {
                        $let: {
                            vars: {
                                ratings: '$ratingDistribution',
                                total: '$totalReviews'
                            },
                            in: {
                                '5': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 5] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 5] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '4': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 4] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 4] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '3': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 3] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 3] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '2': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 2] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 2] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '1': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 1] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 1] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ]);

        const result = ratingData[0] || {
            totalReviews: 0,
            averageRating: 0,
            ratingBreakdown: {
                '5': { count: 0, percentage: 0 },
                '4': { count: 0, percentage: 0 },
                '3': { count: 0, percentage: 0 },
                '2': { count: 0, percentage: 0 },
                '1': { count: 0, percentage: 0 }
            }
        };

        // Add rating quality indicator
        result.ratingQuality = getRatingQuality(result.averageRating);
        result.recommendationPercentage = getRecommendationPercentage(result.ratingBreakdown);

        logger.info(`Overall rating fetched for filters: ${JSON.stringify(filters)}`);
        return result;
    } catch (error) {
        logger.error(`Error fetching overall rating: ${error.message}`);
        throw error;
    }
};

/**
 * Moderate review (admin only)
 * @param {string} reviewId - Review ID
 * @param {Object} moderationData - Moderation data
 * @param {string} adminId - Admin ID from auth
 * @returns {Promise<Object>} Updated review
 */
const moderateReview = async (reviewId, moderationData, adminId) => {
    try {
        const review = await Review.findOne({
            reviewId,
            isDeleted: false
        });

        if (!review) {
            throw new Error('Review not found.');
        }

        review.status = moderationData.status;
        review.moderationReason = moderationData.moderationReason || '';
        review.moderatedBy = adminId;
        review.moderatedAt = new Date();

        const updatedReview = await review.save();
        logger.info(`Review moderated successfully. ID: ${reviewId}, Status: ${moderationData.status}`);

        return updatedReview;
    } catch (error) {
        logger.error(`Error moderating review: ${error.message}`);
        throw error;
    }
};

/**
 * Get rating quality indicator based on average rating
 * @param {number} averageRating - Average rating value
 * @returns {string} Quality indicator
 */
const getRatingQuality = (averageRating) => {
    if (averageRating >= 4.5) return 'Excellent';
    if (averageRating >= 4.0) return 'Very Good';
    if (averageRating >= 3.5) return 'Good';
    if (averageRating >= 3.0) return 'Average';
    if (averageRating >= 2.0) return 'Below Average';
    return 'Poor';
};

/**
 * Calculate recommendation percentage based on 4-5 star ratings
 * @param {Object} ratingBreakdown - Rating breakdown object
 * @returns {number} Recommendation percentage
 */
const getRecommendationPercentage = (ratingBreakdown) => {
    const positiveRatings = ratingBreakdown['5'].count + ratingBreakdown['4'].count;
    const totalRatings = Object.values(ratingBreakdown).reduce((sum, rating) => sum + rating.count, 0);

    if (totalRatings === 0) return 0;
    return Math.round((positiveRatings / totalRatings) * 100);
};

/**
 * Get quick rating summary for multiple items
 * @param {Array} itemIds - Array of service or provider IDs
 * @param {string} itemType - Type of item ('serviceId' or 'providerId')
 * @returns {Promise<Array>} Array of rating summaries
 */
const getMultipleRatingSummaries = async (itemIds, itemType) => {
    try {
        const matchStage = {
            isDeleted: false,
            status: 'approved',
            [itemType]: { $in: itemIds }
        };

        const summaries = await Review.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: `$${itemType}`,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' }
                }
            },
            {
                $project: {
                    _id: 0,
                    itemId: '$_id',
                    totalReviews: 1,
                    averageRating: { $round: ['$averageRating', 2] }
                }
            }
        ]);

        // Create a map for quick lookup
        const summaryMap = {};
        summaries.forEach(summary => {
            summaryMap[summary.itemId] = {
                totalReviews: summary.totalReviews,
                averageRating: summary.averageRating,
                ratingQuality: getRatingQuality(summary.averageRating)
            };
        });

        // Ensure all requested items have a summary (even if no reviews)
        const result = itemIds.map(itemId => ({
            itemId,
            totalReviews: summaryMap[itemId]?.totalReviews || 0,
            averageRating: summaryMap[itemId]?.averageRating || 0,
            ratingQuality: summaryMap[itemId]?.ratingQuality || 'No Reviews'
        }));

        logger.info(`Multiple rating summaries fetched for ${itemIds.length} ${itemType}s`);
        return result;
    } catch (error) {
        logger.error(`Error fetching multiple rating summaries: ${error.message}`);
        throw error;
    }
};

/**
 * Get reviews with advanced filtering for frontend display
 * @param {Object} options - Query options
 * @returns {Promise<Object>} Reviews with enhanced data
 */
const getReviewsForFrontend = async (options = {}) => {
    try {
        const {
            serviceId,
            providerId,
            customerId,
            rating,
            hasImages = null,
            hasProviderResponse = null,
            sortBy = 'reviewDate',
            sortOrder = 'desc',
            page = 1,
            limit = 10,
            search = null,
            dateFrom = null,
            dateTo = null,
            minHelpfulVotes = null
        } = options;

        // Build match conditions
        const matchConditions = {
            isDeleted: false,
            status: 'approved'
        };

        if (serviceId) matchConditions.serviceId = serviceId;
        if (providerId) matchConditions.providerId = providerId;
        if (customerId) matchConditions.customerId = customerId;
        if (rating) matchConditions.rating = parseInt(rating);

        // Date range filter
        if (dateFrom || dateTo) {
            matchConditions.reviewDate = {};
            if (dateFrom) matchConditions.reviewDate.$gte = new Date(dateFrom);
            if (dateTo) matchConditions.reviewDate.$lte = new Date(dateTo);
        }

        // Text search
        if (search) {
            matchConditions.$or = [
                { title: { $regex: search, $options: 'i' } },
                { comment: { $regex: search, $options: 'i' } }
            ];
        }

        const skip = (page - 1) * limit;
        const sortDirection = sortOrder === 'desc' ? -1 : 1;

        const pipeline = [
            { $match: matchConditions },
            {
                $addFields: {
                    helpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'helpful'] }
                            }
                        }
                    },
                    notHelpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'not_helpful'] }
                            }
                        }
                    },
                    imageCount: { $size: '$images' },
                    hasImages: { $gt: [{ $size: '$images' }, 0] },
                    hasProviderResponseField: {
                        $cond: {
                            if: { $ifNull: ['$providerResponse', false] },
                            then: true,
                            else: false
                        }
                    }
                }
            }
        ];

        // Additional filters based on computed fields
        const additionalMatch = {};
        if (hasImages !== null) additionalMatch.hasImages = hasImages;
        if (hasProviderResponse !== null) additionalMatch.hasProviderResponseField = hasProviderResponse;
        if (minHelpfulVotes !== null) additionalMatch.helpfulVotesCount = { $gte: parseInt(minHelpfulVotes) };

        if (Object.keys(additionalMatch).length > 0) {
            pipeline.push({ $match: additionalMatch });
        }

        // Add final projection and sorting
        pipeline.push(
            {
                $project: {
                    reviewId: 1,
                    customerId: 1,
                    providerId: 1,
                    serviceId: 1,
                    bookingId: 1,
                    rating: 1,
                    title: 1,
                    comment: 1,
                    status: 1,
                    isVerifiedPurchase: 1,
                    isEdited: 1,
                    reviewDate: 1,
                    images: 1,
                    imageCount: 1,
                    hasImages: 1,
                    providerResponse: 1,
                    hasProviderResponse: '$hasProviderResponseField',
                    helpfulVotesCount: 1,
                    notHelpfulVotesCount: 1,
                    totalVotes: { $add: ['$helpfulVotesCount', '$notHelpfulVotesCount'] },
                    helpfulnessPercentage: {
                        $cond: {
                            if: { $eq: [{ $add: ['$helpfulVotesCount', '$notHelpfulVotesCount'] }, 0] },
                            then: 0,
                            else: {
                                $round: [
                                    {
                                        $multiply: [
                                            {
                                                $divide: [
                                                    '$helpfulVotesCount',
                                                    { $add: ['$helpfulVotesCount', '$notHelpfulVotesCount'] }
                                                ]
                                            },
                                            100
                                        ]
                                    },
                                    1
                                ]
                            }
                        }
                    },
                    createdAt: 1,
                    updatedAt: 1
                }
            },
            { $sort: { [sortBy]: sortDirection } },
            { $skip: skip },
            { $limit: limit }
        );

        const reviews = await Review.aggregate(pipeline);

        // Get total count for the same filters
        const countPipeline = [
            { $match: matchConditions },
            {
                $addFields: {
                    helpfulVotesCount: {
                        $size: {
                            $filter: {
                                input: '$helpfulVotes',
                                cond: { $eq: ['$$this.voteType', 'helpful'] }
                            }
                        }
                    },
                    imageCount: { $size: '$images' },
                    hasImages: { $gt: [{ $size: '$images' }, 0] },
                    hasProviderResponseField: {
                        $cond: {
                            if: { $ifNull: ['$providerResponse', false] },
                            then: true,
                            else: false
                        }
                    }
                }
            }
        ];

        if (Object.keys(additionalMatch).length > 0) {
            countPipeline.push({ $match: additionalMatch });
        }

        countPipeline.push({ $count: 'total' });

        const countResult = await Review.aggregate(countPipeline);
        const total = countResult[0]?.total || 0;

        const totalPages = Math.ceil(total / limit);

        logger.info(`Fetched ${reviews.length} reviews for frontend with advanced filtering`);

        return {
            reviews,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit,
                hasNext: page < totalPages,
                hasPrev: page > 1,
                startIndex: skip + 1,
                endIndex: Math.min(skip + limit, total)
            },
            filters: {
                serviceId,
                providerId,
                customerId,
                rating,
                hasImages,
                hasProviderResponse,
                search,
                dateFrom,
                dateTo,
                minHelpfulVotes
            }
        };
    } catch (error) {
        logger.error(`Error fetching reviews for frontend: ${error.message}`);
        throw error;
    }
};

module.exports = {
    createReview,
    getReviews,
    getReviewById,
    getReviewsForFrontend,
    updateReview,
    deleteReview,
    addProviderResponse,
    addHelpfulVote,
    getReviewAnalytics,
    getOverallRating,
    getMultipleRatingSummaries,
    moderateReview,
};
